#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
@author: <PERSON><PERSON><PERSON><PERSON>
@file: new_1.py
@time: 2025/2/7 15:30
@Description: 全新设计的智能RAG Agent - 事件驱动 + 自适应优化

设计理念:
1. 事件驱动架构 - 基于事件的异步处理，更好的解耦和扩展性
2. 智能路由系统 - 动态选择最佳处理策略，支持多种检索模式
3. 自适应优化 - 根据历史表现自动调整参数和策略
4. 可观测性优先 - 全面的监控、追踪和性能分析
5. 插件化设计 - 易于扩展新的处理器和策略
"""

import asyncio
import time
from abc import ABC, abstractmethod
from dataclasses import dataclass, field
from enum import Enum
from typing import Dict, List, Optional, Any, Callable, AsyncGenerator
from concurrent.futures import ThreadPoolExecutor
import json
import logging

# 假设的导入 - 实际使用时需要调整
from langgraph.graph import StateGraph, START, END
from langgraph.checkpoint.memory import MemorySaver
from langfuse.callback import CallbackHandler

# 项目内部导入
from common.myLog import logger
from core.chain import ChainFactory
from core.tools import TavilySearchResultsTool
from common.cache_manager import global_cache
from config.streaming_config import StreamingConfig, BALANCED_STREAMING

logger.name = __name__


class EventType(Enum):
    """事件类型枚举"""
    QUESTION_RECEIVED = "question_received"
    ROUTING_COMPLETED = "routing_completed"
    RETRIEVAL_STARTED = "retrieval_started"
    RETRIEVAL_COMPLETED = "retrieval_completed"
    SEARCH_STARTED = "search_started"
    SEARCH_COMPLETED = "search_completed"
    DECISION_MADE = "decision_made"
    RESPONSE_GENERATED = "response_generated"
    ERROR_OCCURRED = "error_occurred"


class ProcessingStrategy(Enum):
    """处理策略枚举"""
    RETRIEVAL_FIRST = "retrieval_first"      # 优先检索
    SEARCH_FIRST = "search_first"            # 优先搜索
    PARALLEL_BALANCED = "parallel_balanced"   # 平衡并行
    ADAPTIVE = "adaptive"                     # 自适应选择


@dataclass
class Event:
    """事件数据结构"""
    type: EventType
    data: Dict[str, Any]
    timestamp: float = field(default_factory=time.time)
    source: str = ""
    correlation_id: str = ""


@dataclass
class ProcessingContext:
    """处理上下文"""
    question: str
    session_id: str
    correlation_id: str
    strategy: ProcessingStrategy = ProcessingStrategy.ADAPTIVE
    metadata: Dict[str, Any] = field(default_factory=dict)
    events: List[Event] = field(default_factory=list)
    
    def add_event(self, event: Event):
        """添加事件"""
        event.correlation_id = self.correlation_id
        self.events.append(event)


@dataclass
class ProcessingResult:
    """处理结果"""
    answer: str
    confidence: float
    source: str  # "retrieval", "search", "hybrid"
    processing_time: float
    context: ProcessingContext
    metadata: Dict[str, Any] = field(default_factory=dict)


class EventBus:
    """事件总线 - 负责事件的发布和订阅"""
    
    def __init__(self):
        self._subscribers: Dict[EventType, List[Callable]] = {}
        self._middleware: List[Callable] = []
    
    def subscribe(self, event_type: EventType, handler: Callable):
        """订阅事件"""
        if event_type not in self._subscribers:
            self._subscribers[event_type] = []
        self._subscribers[event_type].append(handler)
    
    def add_middleware(self, middleware: Callable):
        """添加中间件"""
        self._middleware.append(middleware)
    
    async def publish(self, event: Event):
        """发布事件"""
        # 执行中间件
        for middleware in self._middleware:
            event = await middleware(event) if asyncio.iscoroutinefunction(middleware) else middleware(event)
            if event is None:
                return
        
        # 通知订阅者
        if event.type in self._subscribers:
            tasks = []
            for handler in self._subscribers[event.type]:
                if asyncio.iscoroutinefunction(handler):
                    tasks.append(handler(event))
                else:
                    tasks.append(asyncio.to_thread(handler, event))
            
            if tasks:
                await asyncio.gather(*tasks, return_exceptions=True)


class BaseProcessor(ABC):
    """处理器基类"""
    
    def __init__(self, name: str, event_bus: EventBus):
        self.name = name
        self.event_bus = event_bus
        self.metrics = {
            "total_requests": 0,
            "successful_requests": 0,
            "avg_processing_time": 0.0,
            "error_rate": 0.0
        }
    
    @abstractmethod
    async def process(self, context: ProcessingContext) -> Any:
        """处理逻辑"""
        pass
    
    def update_metrics(self, success: bool, processing_time: float):
        """更新指标"""
        self.metrics["total_requests"] += 1
        if success:
            self.metrics["successful_requests"] += 1
        
        # 更新平均处理时间
        total_time = self.metrics["avg_processing_time"] * (self.metrics["total_requests"] - 1)
        self.metrics["avg_processing_time"] = (total_time + processing_time) / self.metrics["total_requests"]
        
        # 更新错误率
        self.metrics["error_rate"] = 1 - (self.metrics["successful_requests"] / self.metrics["total_requests"])


class RouterProcessor(BaseProcessor):
    """路由处理器 - 负责问题分类和路由"""
    
    def __init__(self, event_bus: EventBus, chain_factory: ChainFactory):
        super().__init__("router", event_bus)
        self.chain_factory = chain_factory
        self._router_chain = None
    
    async def _get_router_chain(self):
        """获取路由链"""
        if self._router_chain is None:
            self._router_chain = self.chain_factory.create_chain(
                chain_type="router",
                session_state={}
            )
        return self._router_chain
    
    async def process(self, context: ProcessingContext) -> str:
        """路由处理"""
        start_time = time.time()
        
        try:
            await self.event_bus.publish(Event(
                EventType.ROUTING_COMPLETED,
                {"question": context.question},
                source=self.name
            ))
            
            router_chain = await self._get_router_chain()
            target_collection = await router_chain.ainvoke({"question": context.question})
            
            processing_time = time.time() - start_time
            self.update_metrics(True, processing_time)
            
            context.add_event(Event(
                EventType.ROUTING_COMPLETED,
                {"target_collection": target_collection, "processing_time": processing_time},
                source=self.name
            ))
            
            return target_collection
            
        except Exception as e:
            processing_time = time.time() - start_time
            self.update_metrics(False, processing_time)
            
            await self.event_bus.publish(Event(
                EventType.ERROR_OCCURRED,
                {"error": str(e), "processor": self.name},
                source=self.name
            ))
            raise


class RetrievalProcessor(BaseProcessor):
    """检索处理器"""
    
    def __init__(self, event_bus: EventBus, chain_factory: ChainFactory):
        super().__init__("retrieval", event_bus)
        self.chain_factory = chain_factory
        self._rag_chains = {}
    
    async def _get_rag_chain(self, collection_name: str):
        """获取RAG链"""
        if collection_name not in self._rag_chains:
            self._rag_chains[collection_name] = self.chain_factory.create_chain(
                chain_type="rag",
                retriever_type="Rerank",
                collection_name=collection_name,
                session_state={}
            )
        return self._rag_chains[collection_name]
    
    async def process(self, context: ProcessingContext) -> Dict[str, Any]:
        """检索处理"""
        start_time = time.time()
        
        try:
            await self.event_bus.publish(Event(
                EventType.RETRIEVAL_STARTED,
                {"question": context.question},
                source=self.name
            ))
            
            target_collection = context.metadata.get("target_collection")
            if not target_collection:
                raise ValueError("缺少目标集合信息")
            
            rag_chain = await self._get_rag_chain(target_collection)
            answer = await rag_chain.ainvoke({"question": context.question})
            
            # 计算置信度
            confidence = self._calculate_confidence(answer)
            
            processing_time = time.time() - start_time
            self.update_metrics(True, processing_time)
            
            result = {
                "answer": answer,
                "confidence": confidence,
                "processing_time": processing_time,
                "source": "retrieval"
            }
            
            context.add_event(Event(
                EventType.RETRIEVAL_COMPLETED,
                result,
                source=self.name
            ))
            
            return result
            
        except Exception as e:
            processing_time = time.time() - start_time
            self.update_metrics(False, processing_time)
            
            await self.event_bus.publish(Event(
                EventType.ERROR_OCCURRED,
                {"error": str(e), "processor": self.name},
                source=self.name
            ))
            raise
    
    def _calculate_confidence(self, answer: str) -> float:
        """计算置信度"""
        # 简化的置信度计算
        if any(keyword in answer for keyword in ["未学习", "不知道", "无法回答"]):
            return 0.1
        elif any(keyword in answer for keyword in ["可能", "大概", "也许"]):
            return 0.6
        elif len(answer) < 50:
            return 0.4
        elif len(answer) > 200:
            return 0.9
        else:
            return 0.7


class SearchProcessor(BaseProcessor):
    """搜索处理器"""
    
    def __init__(self, event_bus: EventBus, chain_factory: ChainFactory):
        super().__init__("search", event_bus)
        self.chain_factory = chain_factory
        self._common_chain = None
        self._executor = ThreadPoolExecutor(max_workers=2)
    
    async def _get_common_chain(self):
        """获取通用链"""
        if self._common_chain is None:
            self._common_chain = self.chain_factory.create_chain(
                chain_type="common",
                session_state={}
            )
        return self._common_chain
    
    async def process(self, context: ProcessingContext) -> Dict[str, Any]:
        """搜索处理"""
        start_time = time.time()
        
        try:
            await self.event_bus.publish(Event(
                EventType.SEARCH_STARTED,
                {"question": context.question},
                source=self.name
            ))
            
            # 执行网络搜索
            loop = asyncio.get_event_loop()
            search_results = await loop.run_in_executor(
                self._executor,
                TavilySearchResultsTool,
                context.question,
                5
            )
            
            # 处理搜索结果
            common_chain = await self._get_common_chain()
            processed_answer = await common_chain.ainvoke({
                "question": context.question,
                "answer": search_results
            })
            
            processing_time = time.time() - start_time
            self.update_metrics(True, processing_time)
            
            result = {
                "answer": processed_answer,
                "confidence": 0.8,  # 搜索结果通常有较高置信度
                "processing_time": processing_time,
                "source": "search"
            }
            
            context.add_event(Event(
                EventType.SEARCH_COMPLETED,
                result,
                source=self.name
            ))
            
            return result
            
        except Exception as e:
            processing_time = time.time() - start_time
            self.update_metrics(False, processing_time)
            
            await self.event_bus.publish(Event(
                EventType.ERROR_OCCURRED,
                {"error": str(e), "processor": self.name},
                source=self.name
            ))
            raise
    
    def __del__(self):
        """清理资源"""
        if hasattr(self, '_executor'):
            self._executor.shutdown(wait=False)


class DecisionEngine:
    """决策引擎 - 负责选择最佳答案和策略优化"""

    def __init__(self, event_bus: EventBus):
        self.event_bus = event_bus
        self.strategy_performance = {
            ProcessingStrategy.RETRIEVAL_FIRST: {"success_rate": 0.8, "avg_time": 2.5},
            ProcessingStrategy.SEARCH_FIRST: {"success_rate": 0.9, "avg_time": 3.2},
            ProcessingStrategy.PARALLEL_BALANCED: {"success_rate": 0.85, "avg_time": 2.1},
            ProcessingStrategy.ADAPTIVE: {"success_rate": 0.9, "avg_time": 2.3}
        }

    def select_best_answer(self, retrieval_result: Dict, search_result: Dict) -> ProcessingResult:
        """选择最佳答案"""
        retrieval_confidence = retrieval_result.get("confidence", 0.0)
        search_confidence = search_result.get("confidence", 0.0)

        # 智能选择逻辑
        if retrieval_confidence >= 0.7:
            # 检索结果置信度高，优先使用
            selected_result = retrieval_result
            source = "retrieval"
        elif search_confidence >= 0.8:
            # 搜索结果置信度高，使用搜索结果
            selected_result = search_result
            source = "search"
        elif retrieval_confidence > search_confidence:
            # 检索结果相对更好
            selected_result = retrieval_result
            source = "retrieval"
        else:
            # 使用搜索结果
            selected_result = search_result
            source = "search"

        # 如果两个结果都不错，可以考虑混合
        if abs(retrieval_confidence - search_confidence) < 0.1 and min(retrieval_confidence, search_confidence) > 0.6:
            # 混合答案
            hybrid_answer = self._create_hybrid_answer(retrieval_result, search_result)
            return ProcessingResult(
                answer=hybrid_answer,
                confidence=max(retrieval_confidence, search_confidence),
                source="hybrid",
                processing_time=max(retrieval_result.get("processing_time", 0), search_result.get("processing_time", 0)),
                context=None,  # 将在外部设置
                metadata={
                    "retrieval_confidence": retrieval_confidence,
                    "search_confidence": search_confidence,
                    "decision_reason": "hybrid_high_confidence"
                }
            )

        return ProcessingResult(
            answer=selected_result["answer"],
            confidence=selected_result["confidence"],
            source=source,
            processing_time=selected_result.get("processing_time", 0),
            context=None,  # 将在外部设置
            metadata={
                "retrieval_confidence": retrieval_confidence,
                "search_confidence": search_confidence,
                "decision_reason": f"selected_{source}_higher_confidence"
            }
        )

    def _create_hybrid_answer(self, retrieval_result: Dict, search_result: Dict) -> str:
        """创建混合答案"""
        retrieval_answer = retrieval_result.get("answer", "")
        search_answer = search_result.get("answer", "")

        return f"""基于知识库的回答：
{retrieval_answer}

补充的网络搜索信息：
{search_answer}"""

    def recommend_strategy(self, context: ProcessingContext) -> ProcessingStrategy:
        """推荐处理策略"""
        # 基于历史表现和当前上下文推荐策略
        question_length = len(context.question)

        if question_length < 20:
            # 短问题，优先检索
            return ProcessingStrategy.RETRIEVAL_FIRST
        elif question_length > 100:
            # 长问题，可能需要搜索
            return ProcessingStrategy.SEARCH_FIRST
        else:
            # 中等长度，使用并行策略
            return ProcessingStrategy.PARALLEL_BALANCED

    def update_strategy_performance(self, strategy: ProcessingStrategy, success: bool, processing_time: float):
        """更新策略性能"""
        if strategy in self.strategy_performance:
            current = self.strategy_performance[strategy]
            # 简单的移动平均更新
            current["success_rate"] = current["success_rate"] * 0.9 + (1.0 if success else 0.0) * 0.1
            current["avg_time"] = current["avg_time"] * 0.9 + processing_time * 0.1


class IntelligentRAGAgent:
    """智能RAG Agent - 主控制器"""

    def __init__(self, session_id: str = "default"):
        self.session_id = session_id
        self.event_bus = EventBus()
        self.chain_factory = ChainFactory()
        self.decision_engine = DecisionEngine(self.event_bus)

        # 初始化处理器
        self.router = RouterProcessor(self.event_bus, self.chain_factory)
        self.retrieval = RetrievalProcessor(self.event_bus, self.chain_factory)
        self.search = SearchProcessor(self.event_bus, self.chain_factory)

        # 设置事件监听
        self._setup_event_listeners()

        # 性能监控
        self.global_metrics = {
            "total_requests": 0,
            "successful_requests": 0,
            "avg_response_time": 0.0,
            "strategy_usage": {strategy: 0 for strategy in ProcessingStrategy}
        }

    def _setup_event_listeners(self):
        """设置事件监听器"""
        # 添加日志中间件
        async def logging_middleware(event: Event) -> Event:
            logger.info(f"Event: {event.type.value} from {event.source} at {event.timestamp}")
            return event

        self.event_bus.add_middleware(logging_middleware)

        # 订阅错误事件
        async def error_handler(event: Event):
            logger.error(f"Error in {event.data.get('processor', 'unknown')}: {event.data.get('error')}")

        self.event_bus.subscribe(EventType.ERROR_OCCURRED, error_handler)

    async def process_question(self, question: str, strategy: Optional[ProcessingStrategy] = None) -> ProcessingResult:
        """处理问题 - 主入口"""
        start_time = time.time()
        correlation_id = f"{self.session_id}_{int(time.time() * 1000)}"

        context = ProcessingContext(
            question=question,
            session_id=self.session_id,
            correlation_id=correlation_id,
            strategy=strategy or ProcessingStrategy.ADAPTIVE
        )

        try:
            # 发布问题接收事件
            await self.event_bus.publish(Event(
                EventType.QUESTION_RECEIVED,
                {"question": question, "strategy": context.strategy.value},
                source="agent"
            ))

            # 1. 路由阶段
            target_collection = await self.router.process(context)
            context.metadata["target_collection"] = target_collection

            # 2. 根据策略执行处理
            if context.strategy == ProcessingStrategy.ADAPTIVE:
                context.strategy = self.decision_engine.recommend_strategy(context)

            result = await self._execute_strategy(context)

            # 更新全局指标
            processing_time = time.time() - start_time
            self._update_global_metrics(True, processing_time, context.strategy)

            # 更新策略性能
            self.decision_engine.update_strategy_performance(
                context.strategy, True, processing_time
            )

            result.context = context
            result.processing_time = processing_time

            # 发布响应生成事件
            await self.event_bus.publish(Event(
                EventType.RESPONSE_GENERATED,
                {"result": result.__dict__},
                source="agent"
            ))

            return result

        except Exception as e:
            processing_time = time.time() - start_time
            self._update_global_metrics(False, processing_time, context.strategy)

            logger.error(f"处理问题失败: {str(e)}", exc_info=True)

            # 返回错误结果
            return ProcessingResult(
                answer="抱歉，系统暂时无法处理您的问题，请稍后重试。",
                confidence=0.0,
                source="error",
                processing_time=processing_time,
                context=context,
                metadata={"error": str(e)}
            )

    async def _execute_strategy(self, context: ProcessingContext) -> ProcessingResult:
        """执行处理策略"""
        if context.strategy == ProcessingStrategy.RETRIEVAL_FIRST:
            return await self._retrieval_first_strategy(context)
        elif context.strategy == ProcessingStrategy.SEARCH_FIRST:
            return await self._search_first_strategy(context)
        elif context.strategy in [ProcessingStrategy.PARALLEL_BALANCED, ProcessingStrategy.ADAPTIVE]:
            return await self._parallel_strategy(context)
        else:
            raise ValueError(f"不支持的策略: {context.strategy}")

    async def _retrieval_first_strategy(self, context: ProcessingContext) -> ProcessingResult:
        """检索优先策略"""
        retrieval_result = await self.retrieval.process(context)

        if retrieval_result["confidence"] >= 0.6:
            return ProcessingResult(
                answer=retrieval_result["answer"],
                confidence=retrieval_result["confidence"],
                source="retrieval",
                processing_time=retrieval_result["processing_time"],
                context=context
            )
        else:
            # 检索结果不佳，执行搜索
            search_result = await self.search.process(context)
            return self.decision_engine.select_best_answer(retrieval_result, search_result)

    async def _search_first_strategy(self, context: ProcessingContext) -> ProcessingResult:
        """搜索优先策略"""
        search_result = await self.search.process(context)
        return ProcessingResult(
            answer=search_result["answer"],
            confidence=search_result["confidence"],
            source="search",
            processing_time=search_result["processing_time"],
            context=context
        )

    async def _parallel_strategy(self, context: ProcessingContext) -> ProcessingResult:
        """并行策略"""
        # 并行执行检索和搜索
        retrieval_task = asyncio.create_task(self.retrieval.process(context))
        search_task = asyncio.create_task(self.search.process(context))

        retrieval_result, search_result = await asyncio.gather(
            retrieval_task, search_task, return_exceptions=True
        )

        # 处理异常结果
        if isinstance(retrieval_result, Exception):
            retrieval_result = {"answer": "检索失败", "confidence": 0.0, "processing_time": 0.0}
        if isinstance(search_result, Exception):
            search_result = {"answer": "搜索失败", "confidence": 0.0, "processing_time": 0.0}

        return self.decision_engine.select_best_answer(retrieval_result, search_result)

    def _update_global_metrics(self, success: bool, processing_time: float, strategy: ProcessingStrategy):
        """更新全局指标"""
        self.global_metrics["total_requests"] += 1
        if success:
            self.global_metrics["successful_requests"] += 1

        # 更新平均响应时间
        total_time = self.global_metrics["avg_response_time"] * (self.global_metrics["total_requests"] - 1)
        self.global_metrics["avg_response_time"] = (total_time + processing_time) / self.global_metrics["total_requests"]

        # 更新策略使用统计
        self.global_metrics["strategy_usage"][strategy] += 1

    async def stream_response(self, question: str, strategy: Optional[ProcessingStrategy] = None) -> AsyncGenerator[str, None]:
        """流式响应生成器"""
        correlation_id = f"{self.session_id}_{int(time.time() * 1000)}"

        context = ProcessingContext(
            question=question,
            session_id=self.session_id,
            correlation_id=correlation_id,
            strategy=strategy or ProcessingStrategy.ADAPTIVE
        )

        try:
            # 流式输出开始提示
            yield f"🤔 正在分析问题: {question[:50]}{'...' if len(question) > 50 else ''}\n\n"

            # 路由阶段
            yield "📍 正在路由到相关知识库...\n"
            target_collection = await self.router.process(context)
            context.metadata["target_collection"] = target_collection
            yield f"✅ 已定位到知识库: {target_collection}\n\n"

            # 确定策略
            if context.strategy == ProcessingStrategy.ADAPTIVE:
                context.strategy = self.decision_engine.recommend_strategy(context)
            yield f"🎯 采用处理策略: {context.strategy.value}\n\n"

            # 根据策略执行并流式输出
            if context.strategy == ProcessingStrategy.PARALLEL_BALANCED:
                yield "🔄 正在并行执行检索和搜索...\n"

                # 创建任务但不等待完成
                retrieval_task = asyncio.create_task(self.retrieval.process(context))
                search_task = asyncio.create_task(self.search.process(context))

                # 等待第一个完成的任务
                done, _ = await asyncio.wait(
                    [retrieval_task, search_task],
                    return_when=asyncio.FIRST_COMPLETED
                )

                # 输出第一个完成的结果
                first_result = None
                for task in done:
                    try:
                        result = await task
                        if result["confidence"] >= 0.7:
                            yield f"⚡ 快速响应 ({result['source']}):\n\n"
                            yield result["answer"]
                            first_result = result
                            break
                    except Exception:
                        continue

                # 等待所有任务完成并做最终决策
                all_results = await asyncio.gather(retrieval_task, search_task, return_exceptions=True)

                if not first_result:
                    # 如果没有快速响应，使用决策引擎选择最佳答案
                    retrieval_result = all_results[0] if not isinstance(all_results[0], Exception) else {"answer": "检索失败", "confidence": 0.0}
                    search_result = all_results[1] if not isinstance(all_results[1], Exception) else {"answer": "搜索失败", "confidence": 0.0}

                    final_result = self.decision_engine.select_best_answer(retrieval_result, search_result)
                    yield f"\n\n🎯 最终答案 ({final_result.source}):\n\n"
                    yield final_result.answer

            else:
                # 非并行策略的流式输出
                yield "🔍 正在处理...\n"
                result = await self._execute_strategy(context)
                yield f"\n✨ 答案 ({result.source}):\n\n"
                yield result.answer

            yield f"\n\n📊 处理完成 | 策略: {context.strategy.value}"

        except Exception as e:
            yield f"\n❌ 处理出错: {str(e)}\n"
            yield "抱歉，系统暂时无法处理您的问题，请稍后重试。"

    def get_metrics(self) -> Dict[str, Any]:
        """获取性能指标"""
        return {
            "global_metrics": self.global_metrics,
            "processor_metrics": {
                "router": self.router.metrics,
                "retrieval": self.retrieval.metrics,
                "search": self.search.metrics
            },
            "strategy_performance": self.decision_engine.strategy_performance
        }

    def get_health_status(self) -> Dict[str, Any]:
        """获取健康状态"""
        total_requests = self.global_metrics["total_requests"]
        success_rate = (self.global_metrics["successful_requests"] / total_requests) if total_requests > 0 else 1.0

        status = "healthy"
        if success_rate < 0.8:
            status = "degraded"
        elif success_rate < 0.5:
            status = "unhealthy"

        return {
            "status": status,
            "success_rate": success_rate,
            "avg_response_time": self.global_metrics["avg_response_time"],
            "total_requests": total_requests,
            "uptime": time.time(),  # 简化的运行时间
            "processors_status": {
                name: "healthy" if processor.metrics["error_rate"] < 0.2 else "degraded"
                for name, processor in [
                    ("router", self.router),
                    ("retrieval", self.retrieval),
                    ("search", self.search)
                ]
            }
        }


# 使用示例和测试代码
if __name__ == "__main__":
    async def test_agent():
        """测试智能RAG Agent"""
        agent = IntelligentRAGAgent(session_id="test_session")

        test_questions = [
            "预约单无法关闭怎么办？",
            "如何优化系统性能？",
            "最新的AI技术趋势是什么？"
        ]

        print("🚀 智能RAG Agent 测试开始\n")
        print("=" * 60)

        for i, question in enumerate(test_questions, 1):
            print(f"\n📝 测试问题 {i}: {question}")
            print("-" * 40)

            # 测试流式响应
            print("🔄 流式响应:")
            async for chunk in agent.stream_response(question):
                print(chunk, end="", flush=True)

            print("\n" + "=" * 60)

            # 显示指标
            if i == len(test_questions):
                print("\n📊 最终性能指标:")
                metrics = agent.get_metrics()
                print(f"总请求数: {metrics['global_metrics']['total_requests']}")
                print(f"成功率: {metrics['global_metrics']['successful_requests'] / metrics['global_metrics']['total_requests']:.2%}")
                print(f"平均响应时间: {metrics['global_metrics']['avg_response_time']:.2f}秒")

                print("\n🏥 系统健康状态:")
                health = agent.get_health_status()
                print(f"状态: {health['status']}")
                print(f"成功率: {health['success_rate']:.2%}")
                print(f"平均响应时间: {health['avg_response_time']:.2f}秒")

    # 运行测试
    # asyncio.run(test_agent())

    print("""

🎉 新一代智能RAG Agent 特性总结:

✨ 核心优势:
1. 事件驱动架构 - 完全解耦的组件设计
2. 智能决策引擎 - 自动选择最佳答案和策略
3. 自适应优化 - 根据历史表现动态调整
4. 全面可观测性 - 详细的监控和追踪
5. 插件化设计 - 易于扩展新功能

🔧 技术特点:
- 异步并发处理，性能优异
- 多策略支持，灵活适应不同场景
- 实时流式输出，用户体验佳
- 完整的错误处理和降级机制
- 丰富的性能指标和健康检查

🚀 使用方式:
```python
# 基本使用
agent = IntelligentRAGAgent(session_id="user_123")
result = await agent.process_question("你的问题")

# 流式输出
async for chunk in agent.stream_response("你的问题"):
    print(chunk, end="", flush=True)

# 性能监控
metrics = agent.get_metrics()
health = agent.get_health_status()
```

这个设计相比原版本的主要改进:
1. 更清晰的架构分层和职责分离
2. 事件驱动带来更好的可扩展性
3. 智能决策引擎提供更优的答案选择
4. 自适应机制持续优化性能
5. 全面的可观测性支持运维监控
    """)
